from stock import get_stock_data
from detector import EventDrivenTurnPointDetector
from signal_gen import TradingSignalGenerator
from backtester import Backtester

data = get_stock_data('003021', '20240101', '20250630')

# 2. 创建转折点检测器
detector = EventDrivenTurnPointDetector(
    min_keep_days=3, 
    min_extent_percent=1.0
)

# 3. 创建信号生成器
signal_generator = TradingSignalGenerator()

# 4. 注册信号处理器
detector.register_handler(signal_generator)

# 5. 处理数据
points, events = detector.process_data(data)

# 6. 创建回测引擎并运行回测
backtester = Backtester(initial_capital=100000)
report = backtester.run_backtest(events)

# 7. 输出结果
print(f"\n{' 回测结果 ':=^50}")
print(f"初始资金: ¥{report['initial_capital']:,.2f}")
print(f"最终资产: ¥{report['final_value']:,.2f}")
print(f"总收益: ¥{report['profit']:,.2f} ({report['return_percent']:.2f}%)")
print(f"最大回撤: {report['max_drawdown']*100:.2f}%")
print(f"交易次数: {report['num_trades']}次")
print(f"胜率: {report['win_rate']:.2f}%")
print(f"{'='*50}")

# 8. 可视化结果
import matplotlib.pyplot as plt

plt.figure(figsize=(14, 10))

# 价格曲线
ax1 = plt.subplot(3, 1, 1)
ax1.plot(report['portfolio_history']['date'], report['portfolio_history']['price'], label='Price')
ax1.set_title('Stock Price')
ax1.set_ylabel('Price')

# 标记转折点
for point in points:
    if point.point_type == 1:  # 波峰
        ax1.plot(point.point_date, point.price, 'v', markersize=8, color='red')
    else:  # 波谷
        ax1.plot(point.point_date, point.price, '^', markersize=8, color='green')

# 标记交易信号
buy_dates = [t['date'] for t in report['trade_history'] if t['type'] == 'BUY']
buy_prices = [t['price'] for t in report['trade_history'] if t['type'] == 'BUY']
sell_dates = [t['date'] for t in report['trade_history'] if t['type'] == 'SELL']
sell_prices = [t['price'] for t in report['trade_history'] if t['type'] == 'SELL']

ax1.plot(buy_dates, buy_prices, '^', markersize=10, color='blue', label='Buy')
ax1.plot(sell_dates, sell_prices, 'v', markersize=10, color='black', label='Sell')

# 投资组合价值
ax2 = plt.subplot(3, 1, 2, sharex=ax1)
ax2.plot(report['portfolio_history']['date'], report['portfolio_history']['value'], label='Portfolio Value', color='green')
ax2.set_title('Portfolio Value')
ax2.set_ylabel('Value')

# 持仓和现金
ax3 = plt.subplot(3, 1, 3, sharex=ax1)
ax3.plot(report['portfolio_history']['date'], report['portfolio_history']['position'], label='Position', color='blue')
ax3.set_title('Position and Cash')
ax3.set_ylabel('Shares')

ax3b = ax3.twinx()
ax3b.plot(report['portfolio_history']['date'], report['portfolio_history']['cash'], label='Cash', color='orange')
ax3b.set_ylabel('Cash')

plt.tight_layout()
plt.show()